#!/usr/bin/env python3
"""Create database tables for SmartAnalytics in the common schema.

This script creates all the required tables (dim_tenant, dim_agent, dim_ring_group, dim_agent_event)
in the common schema using the SQLAlchemy models.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the layers to the Python path so we can import them
project_root = Path(__file__).parent
layers_path = project_root / "layers"

# Add each layer to the path
for layer_name in ["utilities", "domain", "infrastructure"]:
    layer_python_path = layers_path / layer_name / "python"
    if layer_python_path.exists():
        sys.path.insert(0, str(layer_python_path))

from smartanalytics_infrastructure.database import Base, get_database_connection
from smartanalytics_utilities.config.settings import get_settings
from sqlalchemy import text


async def create_tables():
    """Create all database tables in the common schema."""
    print("🔧 Creating SmartAnalytics database tables...")
    
    # Set environment variables for database connection
    # These should match your dev environment configuration
    os.environ["DATABASE__HOST"] = "memo-test-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    os.environ["DATABASE__PORT"] = "5432"
    os.environ["DATABASE__DATABASE_NAME"] = "prod"
    os.environ["DATABASE__USERNAME"] = "common_user"
    os.environ["DATABASE__USE_IAM_AUTH"] = "false"  # Use password auth for this script
    os.environ["DATABASE__SCHEMA_NAME"] = "common"
    
    # You'll need to set the password - either via environment variable or prompt
    if "DATABASE__PASSWORD" not in os.environ:
        print("Please set DATABASE__PASSWORD environment variable or update the script with the password")
        return False
    
    settings = get_settings()
    print(f"   Host: {settings.database.host}")
    print(f"   Database: {settings.database.database_name}")
    print(f"   Schema: {settings.database.schema_name}")
    print(f"   Username: {settings.database.username}")
    
    try:
        # Get database connection
        db_conn = get_database_connection()
        engine = db_conn.get_engine()
        
        async with engine.begin() as conn:
            # Create schema if it doesn't exist
            print("Creating schema if it doesn't exist...")
            await conn.execute(text(f'CREATE SCHEMA IF NOT EXISTS "{settings.database.schema_name}"'))
            
            # Create all tables using SQLAlchemy models
            print("Creating tables...")
            
            # Set the schema for all tables in the metadata
            for table in Base.metadata.tables.values():
                table.schema = settings.database.schema_name
            
            # Create all tables using SQLAlchemy
            await conn.run_sync(Base.metadata.create_all)
            
            print("✅ Tables created successfully!")
            
            # Verify tables were created
            print("\nVerifying tables...")
            tables_result = await conn.execute(
                text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = :schema_name
                    ORDER BY table_name
                """),
                {"schema_name": settings.database.schema_name}
            )
            tables = tables_result.fetchall()
            
            if tables:
                print(f"✅ Found {len(tables)} tables in '{settings.database.schema_name}' schema:")
                for table in tables:
                    print(f"   - {table[0]}")
            else:
                print(f"❌ No tables found in '{settings.database.schema_name}' schema")
                return False
                
        await engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main function."""
    print("=" * 80)
    print("SMARTANALYTICS DATABASE TABLE CREATION")
    print("=" * 80)
    
    success = await create_tables()
    
    if success:
        print("\n✅ Database tables created successfully!")
        print("\nYou can now run your Lambda function.")
    else:
        print("\n❌ Failed to create database tables.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())


# Lambda assume role policy document
data "aws_iam_policy_document" "lambda_assume_role" {
  statement {
    effect = "Allow"
    actions = [
      "sts:AssumeRole"
    ]
    principals {
      type = "Service"
      identifiers = [
        "lambda.amazonaws.com"
      ]
    }
  }
}

# IAM execution role for the ACD processor Lambda function
resource "aws_iam_role" "acd_processor_lambda_role" {
  name               = "${var.name_prefix}-acd-processor"
  assume_role_policy = data.aws_iam_policy_document.lambda_assume_role.json

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-acd-processor"
    }
  )
}

# IAM policy document for Lambda
data "aws_iam_policy_document" "acd_processor_lambda_policy" {
  statement {
    sid    = "SecretRead"
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue"
    ]
    resources = [
      "arn:${data.aws_partition.current.partition}:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:${var.database_secret_name}*"
    ]
  }

  statement {
    sid    = "S3LambdaCodeAccess"
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:GetObjectTagging",
    ]
    resources = [
      "arn:${data.aws_partition.current.partition}:s3:::${var.s3_lambda_code_bucket_name}/${var.lambda_zip_file_name}",

    ]
  }

  statement {
    sid    = "KMSDecryptForReads"
    effect = "Allow"
    actions = [
      "kms:Decrypt",
      "kms:GenerateDataKey",
      "kms:GenerateDataKeyWithoutPlaintext"
    ]
    resources = [
      "arn:${data.aws_partition.current.partition}:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:key/${var.kms_key_name}",
      "arn:${data.aws_partition.current.partition}:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:alias/${var.kms_key_name}"
    ]
  }

  # SQS permissions for Lambda event source mapping
  dynamic "statement" {
    for_each = length(local.queue_arns) > 0 ? [1] : []
    content {
      sid    = "SQSAccess"
      effect = "Allow"
      actions = [
        "sqs:SendMessage",
        "sqs:ReceiveMessage",
        "sqs:DeleteMessage",
        "sqs:GetQueueAttributes"
      ]
      resources = local.queue_arns
    }
  }

  # RDS Connect permissions for Aurora PostgreSQL with IAM authentication via RDS Proxy
  statement {
    sid    = "RDSConnect"
    effect = "Allow"
    actions = [
      "rds-db:connect"
    ]
    resources = [
      "arn:${data.aws_partition.current.partition}:rds-db:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:dbuser:${var.rds_proxy_resource_id}/${var.database_user_name}"
    ]
  }

  # RDS IAM token generation permission - CRITICAL for IAM authentication
  statement {
    sid    = "RDSGenerateAuthToken"
    effect = "Allow"
    actions = [
      "rds:GenerateDBAuthToken"
    ]
    resources = ["*"]
  }

  # RDS Describe permissions for cluster and proxy information
  statement {
    sid    = "RDSDescribe"
    effect = "Allow"
    actions = [
      "rds:DescribeDBClusters",
      "rds:DescribeDBInstances",
      "rds:DescribeDBProxies",
      "rds:DescribeDBProxyTargets"
    ]
    resources = ["*"]
  }

  # VPC permissions for Lambda in VPC
  statement {
    sid    = "VPCAccess"
    effect = "Allow"
    actions = [
      "ec2:CreateNetworkInterface",
      "ec2:DescribeNetworkInterfaces",
      "ec2:DescribeSubnets",
      "ec2:DeleteNetworkInterface",
      "ec2:AssignPrivateIpAddresses",
      "ec2:UnassignPrivateIpAddresses"

    ]
    # VPC permissions are scoped to specific VPC and subnets
    resources = [
      "arn:${data.aws_partition.current.partition}:ec2:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:vpc/${var.vpc_id}",
      "arn:${data.aws_partition.current.partition}:ec2:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:subnet/*",
      "arn:${data.aws_partition.current.partition}:ec2:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:network-interface/*",
      "arn:${data.aws_partition.current.partition}:ec2:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:security-group/*"
    ]
  }

  # CloudWatch Logs permissions
  statement {
    sid       = "LogsCreateGroup"
    effect    = "Allow"
    actions   = ["logs:CreateLogGroup"]
    resources = ["*"]
  }

  statement {
    sid     = "LogsWrite"
    effect  = "Allow"
    actions = ["logs:CreateLogStream", "logs:PutLogEvents"]
    resources = [
      "arn:${data.aws_partition.current.partition}:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.name_prefix}-acd-processor:*"
    ]
  }
}

# IAM policy with least-privilege permissions for Lambda function
resource "aws_iam_role_policy" "acd_processor_lambda_policy" {
  role   = aws_iam_role.acd_processor_lambda_role.name
  name   = "SmartAnalyticsACDProcessorPolicy"
  policy = data.aws_iam_policy_document.acd_processor_lambda_policy.json
}

# AWS managed policy attachment for VPC Lambda execution
resource "aws_iam_role_policy_attachment" "acd_processor_lambda_vpc_execution" {
  role       = aws_iam_role.acd_processor_lambda_role.name
  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

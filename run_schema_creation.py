#!/usr/bin/env python3
"""Run the SmartAnalytics schema creation SQL script.

This script executes the create_smartanalytics_schema.sql file against the database
using the master user credentials.
"""

import asyncio
import sys
from pathlib import Path

import asyncpg


async def run_schema_creation():
    """Run the schema creation SQL script."""
    print("🔧 Running SmartAnalytics schema creation script...")
    
    # Master user credentials
    master_username = "Allerium"
    master_password = "72bOkOq23DL3MuUa"
    
    # Database connection details
    host = "memo-test-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    port = 5432
    database = "prod"
    
    print(f"   Host: {host}")
    print(f"   Database: {database}")
    print(f"   Master User: {master_username}")
    
    # Read the SQL script
    sql_file = Path(__file__).parent / "create_smartanalytics_schema.sql"
    if not sql_file.exists():
        print(f"❌ SQL file not found: {sql_file}")
        return False
    
    print(f"   SQL File: {sql_file}")
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        print("✅ SQL script loaded")
        
        # Connect to database
        conn = await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=master_username,
            password=master_password,
            ssl="require"
        )
        
        print("✅ Connected to database")
        
        # Execute the SQL script
        print("Executing schema creation script...")
        await conn.execute(sql_script)
        
        print("✅ Schema creation script executed successfully!")
        
        # Run verification queries
        print("\nVerifying schema creation...")
        
        # Check tables
        tables = await conn.fetch(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'common' ORDER BY table_name"
        )
        
        if tables:
            print(f"✅ Found {len(tables)} tables in 'common' schema:")
            for table in tables:
                print(f"   - {table['table_name']}")
        else:
            print("❌ No tables found in 'common' schema")
            return False
        
        # Check constraints
        constraints = await conn.fetch(
            """
            SELECT constraint_name, constraint_type, table_name 
            FROM information_schema.table_constraints 
            WHERE table_schema = 'common' AND constraint_type IN ('UNIQUE', 'FOREIGN KEY')
            ORDER BY table_name, constraint_type
            """
        )
        
        print(f"\n✅ Found {len(constraints)} constraints:")
        for constraint in constraints[:10]:  # Show first 10
            print(f"   - {constraint['table_name']}.{constraint['constraint_name']} ({constraint['constraint_type']})")
        if len(constraints) > 10:
            print(f"   ... and {len(constraints) - 10} more")
        
        # Check indexes
        indexes = await conn.fetch(
            "SELECT indexname, tablename FROM pg_indexes WHERE schemaname = 'common' ORDER BY tablename, indexname"
        )
        
        print(f"\n✅ Found {len(indexes)} indexes:")
        for index in indexes[:10]:  # Show first 10
            print(f"   - {index['tablename']}.{index['indexname']}")
        if len(indexes) > 10:
            print(f"   ... and {len(indexes) - 10} more")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error running schema creation: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main function."""
    print("=" * 80)
    print("SMARTANALYTICS SCHEMA CREATION")
    print("=" * 80)
    
    success = await run_schema_creation()
    
    if success:
        print("\n✅ SmartAnalytics schema created successfully!")
        print("\nYou can now run your Lambda function.")
        print("\nNext steps:")
        print("1. Test the Lambda function with a sample event")
        print("2. Verify data is being inserted correctly")
    else:
        print("\n❌ Failed to create SmartAnalytics schema.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

# Senior Software Engineer Review: RDS Proxy IAM Authentication Fixes

## Critical Issues Found & Fixed 🚨

### 1. **MISSING IAM Token Generation Permission** ❌➡️✅
**Problem**: <PERSON><PERSON> had NO permission to generate RDS auth tokens
**Impact**: IAM authentication would fail with permission denied errors

**Fixed by adding**:
```hcl
statement {
  sid    = "RDSGenerateAuthToken"
  effect = "Allow"
  actions = [
    "rds:GenerateDBAuthToken"  # CRITICAL - was missing!
  ]
  resources = ["*"]
}
```

### 2. **Misleading Variable Names** ❌➡️✅
**Problem**: Variable names were confusing and incorrect
**Impact**: Configuration errors and maintenance confusion

**Before (Confusing)**:
```hcl
rds_cluster_identifier  = "dev-au-smartanalytics-common-aurora-db-proxy"  # ❌ This is a proxy endpoint!
rds_cluster_resource_id = "prx-003e87a11a1e672f2"                        # ❌ This is a proxy resource ID!
```

**After (Clear)**:
```hcl
rds_proxy_endpoint      = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
rds_proxy_resource_id   = "prx-003e87a11a1e672f2"
```

## Complete IAM Permissions for RDS Proxy Access

### ✅ **Password Authentication** (via Secrets Manager)
```hcl
# Secret access for username/password
statement {
  sid    = "SecretRead"
  effect = "Allow"
  actions = ["secretsmanager:GetSecretValue"]
  resources = ["arn:aws:secretsmanager:region:account:secret:${var.database_secret_name}*"]
}

# KMS for encrypted secrets
statement {
  sid    = "KMSDecryptForReads"
  effect = "Allow"
  actions = ["kms:Decrypt", "kms:GenerateDataKey", "kms:GenerateDataKeyWithoutPlaintext"]
  resources = ["arn:aws:kms:region:account:key/${var.kms_key_name}"]
}
```

### ✅ **IAM Token Authentication** (via RDS IAM)
```hcl
# Generate IAM auth tokens - CRITICAL permission
statement {
  sid    = "RDSGenerateAuthToken"
  effect = "Allow"
  actions = ["rds:GenerateDBAuthToken"]
  resources = ["*"]
}

# Connect using IAM tokens
statement {
  sid    = "RDSConnect"
  effect = "Allow"
  actions = ["rds-db:connect"]
  resources = ["arn:aws:rds-db:region:account:dbuser:${var.rds_proxy_resource_id}/${var.database_user_name}"]
}

# Describe RDS resources for token generation
statement {
  sid    = "RDSDescribe"
  effect = "Allow"
  actions = [
    "rds:DescribeDBClusters",
    "rds:DescribeDBInstances", 
    "rds:DescribeDBProxies",
    "rds:DescribeDBProxyTargets"
  ]
  resources = ["*"]
}
```

## Updated Terragrunt Configuration Required

### Fix Variable Names in `terragrunt.hcl`:
```diff
acd = {
  # Database configuration
  database_secret_name = "dev-au-smartanalytics-common-aurora-credentials-for-db-proxy"
- rds_cluster_identifier  = "dev-au-smartanalytics-common-aurora-db-proxy"
- rds_cluster_resource_id = "prx-003e87a11a1e672f2"
+ rds_proxy_endpoint      = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
+ rds_proxy_resource_id   = "prx-003e87a11a1e672f2"
  
  database_name      = "prod"
  database_user_name = "common_user"
  
  environment_variables = {
    DATABASE__HOST         = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    DATABASE__USERNAME     = "common_user"
    DATABASE__USE_IAM_AUTH = true
    # ... other vars
  }
}
```

## Files Modified

### 1. `terraform/modules/acd-processor/iam.tf`
- ✅ Added `rds:GenerateDBAuthToken` permission
- ✅ Cleaned up duplicate RDS describe statements
- ✅ Updated variable reference to `rds_proxy_resource_id`

### 2. `terraform/modules/acd-processor/variables.tf`
- ✅ Renamed `rds_cluster_identifier` → `rds_proxy_endpoint`
- ✅ Renamed `rds_cluster_resource_id` → `rds_proxy_resource_id`
- ✅ Updated descriptions for clarity

### 3. `terraform/variables.tf`
- ✅ Updated variable definitions to match module changes

### 4. `terraform/main.tf`
- ✅ Updated module parameter passing to use new variable names

## Lambda Connection Capabilities

After these fixes, your Lambda can connect to the database via RDS Proxy using:

### 🔐 **Method 1: Username & Password**
```python
# Lambda reads credentials from Secrets Manager
secret = boto3.client('secretsmanager').get_secret_value(SecretId='secret-name')
creds = json.loads(secret['SecretString'])

conn = asyncpg.connect(
    host="proxy-endpoint",
    user=creds['username'],
    password=creds['password'],
    database="prod"
)
```

### 🎫 **Method 2: IAM Token**
```python
# Lambda generates IAM auth token
rds_client = boto3.client('rds')
token = rds_client.generate_db_auth_token(
    DBHostname="proxy-endpoint",
    Port=5432,
    DBUsername="common_user"
)

conn = asyncpg.connect(
    host="proxy-endpoint", 
    user="common_user",
    password=token,  # IAM token as password
    database="prod"
)
```

## Security Best Practices Implemented ✅

1. **Least Privilege**: Specific resource ARNs where possible
2. **Clear Separation**: Different auth methods use different secrets/users
3. **Proper Naming**: Variables clearly indicate proxy vs cluster resources
4. **Complete Permissions**: All required actions for both auth methods

## Next Steps

1. **Update Terragrunt**: Apply the variable name changes
2. **Deploy**: Run `terragrunt apply` to update IAM permissions
3. **Test**: Verify both authentication methods work
4. **Monitor**: Check CloudWatch logs for any remaining issues

The Lambda now has complete permissions for both username/password and IAM token authentication via RDS Proxy! 🎉
